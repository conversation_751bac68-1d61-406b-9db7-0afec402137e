import {H2} from 'design-v3';
import {useBreakpointName} from 'hooks/useBreakpointName';
import styled from 'styled-components';
import {breakpoints} from 'styles/v3/sizes';
import {reMediaQuery} from 'utils/v3/reMediaQuery';
import {Typography} from 'xsolla/display';
import {ArrowUpRight} from 'xsolla/icons';
import {SharpGroteskMedium20} from 'xsolla/themes/themes/brand/fonts/SharpGroteskMedium20';
import {Container, InnerContainer, Wrapper} from './CommonContainers';

export const Block4 = ({backgroundImage, url, number, description}) => {
  const breakpointName = useBreakpointName(['mobileM', 'tablet', 'laptopL', 'desktop']);
  const arrowIconSize = (() => {
    switch (breakpointName) {
      case 'mobileM':
        return 15;
      case 'tablet':
        return 20;
      case 'laptopL':
        return 32;
      default:
        return 40;
    }
  })();

  return (
    <Container variant="blackV3">
      <BlockInnerContainer>
        <Wrapper>
          <InnerWrapper backgroundImage={backgroundImage}>
            <Link href={url} target="_blank">
              <ArrowUpRight size={arrowIconSize} />
            </Link>
            <Title>{number}</Title>
            <Description>{description}</Description>
          </InnerWrapper>
        </Wrapper>
      </BlockInnerContainer>
    </Container>
  );
};

const BlockInnerContainer = styled(InnerContainer)`
  padding-top: 0 !important;
`;

const InnerWrapper = styled.div<{backgroundImage: string}>`
  background-image: ${({backgroundImage}) => `url(${backgroundImage})`};
  background-size: cover;
  background-position: center;
  width: 100%;
  aspect-ratio: 1.73;
  display: flex;
  flex-direction: column;
  justify-content: end;
  overflow: hidden;

  @media (max-width: ${breakpoints.mobileM}) {
    justify-content: start;
  }

  ${reMediaQuery({
    padding: [14, 30, 48, 60],
    'border-radius': [16, 12, 16, 20]
  })}
`;

const Title = styled(H2)`
  font-family: ${SharpGroteskMedium20.cssFontFamily};

  ${reMediaQuery({
    'font-size': [45, 60, 100, 120]
  })}
`;

const Description = styled(Typography)`
  ${reMediaQuery({
    'font-size': [12, 10, 16, 18]
  })}
`;

const Link = styled.a`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.6);
  color: rgba(255, 255, 255, 0.6);
  ${reMediaQuery({
    width: [25, 40, 64, 80],
    height: [25, 40, 64, 80],
    'margin-bottom': [40, 10, 16, 34]
  })}
`;
