import {H2} from 'design-v3';
import {useState} from 'react';
import styled from 'styled-components';
import {reMediaQuery} from 'utils/v3/reMediaQuery';
import {Typography} from 'xsolla/display';
import {SharpGroteskMedium20} from 'xsolla/themes/themes/brand/fonts/SharpGroteskMedium20';
import {Container, InnerContainer, Wrapper} from './CommonContainers';

export const Block6 = ({backgroundImage, logos, number, description, tabs}) => {
  const defaultActiveTab = tabs.find(tab => tab.active);
  const [selectedTab, setSelectedTab] = useState(defaultActiveTab);

  return (
    <Container variant="blackV3">
      <BlockInnerContainer>
        <Wrapper>
          <InnerWrapper backgroundImage={backgroundImage}>
            <LogoWrapper>
              {logos.map((logo, index) => (
                <Logo key={index}>
                  <img src={logo.src} layout="fill" />
                </Logo>
              ))}
            </LogoWrapper>
            <BoxWrapper>
              <Box>
                <Title>{selectedTab.number}</Title>
                <Description>{description}</Description>
              </Box>
              <TabsWrapper>
                {tabs.map(tab => (
                  <Tab
                    key={tab.title}
                    isActive={selectedTab.title === tab.title}
                    onClick={() => setSelectedTab(tab)}
                  >
                    {tab.title}
                  </Tab>
                ))}
              </TabsWrapper>
            </BoxWrapper>
          </InnerWrapper>
        </Wrapper>
      </BlockInnerContainer>
    </Container>
  );
};

const BlockInnerContainer = styled(InnerContainer)`
  padding-top: 0 !important;
  ${reMediaQuery({
    'padding-bottom': [null, 40, 80, 100]
  })}
`;

const InnerWrapper = styled.div<{backgroundImage?: string}>`
  position: relative;
  border-radius: 20px;
  width: 100%;
  background: ${({backgroundImage}) => (backgroundImage ? `url(${backgroundImage})` : 'none')};
  background-size: cover;
  background-position: center;

  ${reMediaQuery({
    padding: ['24px 14px 14px', '36px 30px 30px', '58px 48px 48px', '72px 60px 60px']
  })}
`;

const LogoWrapper = styled.div`
  display: flex;
  align-items: center;
  ${reMediaQuery({
    gap: [5, 10, 15, 20]
  })}
`;

const Logo = styled.div`
  border: 1px solid rgba(225, 224, 239, 0.1);
  flex: 1 1 auto;
  min-width: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  ${reMediaQuery({
    height: [24, 54, 85, 107],
    'padding-top': [5, 8, 10, 12],
    'padding-bottom': [5, 8, 10, 12],
    'border-radius': [6, 10, 16, 20]
  })}

  & img {
    height: 90%;
  }
`;

const Title = styled(H2)`
  font-family: ${SharpGroteskMedium20.cssFontFamily};

  ${reMediaQuery({
    'font-size': [45, 60, 100, 120]
  })}

  & span {
    color: ${({theme}) => theme.colors.core.text.tertiary};
  }
`;

const Description = styled(Typography)`
  ${reMediaQuery({
    'font-size': [12, 10, 16, 18]
  })}
`;

const BoxWrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  ${reMediaQuery({
    'padding-top': [32, 45, 70, 90]
  })}
`;

const Box = styled.div<{backgroundImage?: string}>`
  display: flex;
  flex-direction: column;
  background: ${({backgroundImage}) => (backgroundImage ? `url(${backgroundImage})` : 'none')};
  background-size: cover;
  background-position: center;
`;

const TabsWrapper = styled.div`
  justify-self: end;
  align-self: end;
  display: flex;
  align-items: center;
  border: 1px solid rgba(225, 224, 239, 0.1);
  border-radius: 60px;
  width: fit-content;
  height: fit-content;
  ${reMediaQuery({
    padding: ['6px 10px', '9px 14px', '14px 23px', '18px 30px'],
    gap: [12, 18, 27, 34]
  })}
`;

const Tab = styled(Typography)<{isActive: boolean}>`
  width: fit-content;
  cursor: pointer;
  line-height: 150%;
  color: ${({isActive, theme}) =>
    isActive ? theme.colors.core.text.primary : theme.colors.core.text.secondary};
  ${reMediaQuery({
    'font-size': [8, 10, 16, 20]
  })}
`;
