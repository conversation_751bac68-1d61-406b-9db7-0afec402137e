import {Arrow} from 'components/Arrow';
import {MainWrapper} from 'components/Carousel/partials/MainWrapper';
import {type CarouselComponentProps} from 'components/Carousel/types';
import {generalSwiperProps, getSlidesOffsetAfter} from 'components/Carousel/utils';
import {Children, type ReactNode, useEffect, useRef, useState} from 'react';
import styled from 'styled-components';
import {breakpoints} from 'styles/v3/sizes';
import {FreeMode, Navigation, Thumbs} from 'swiper';
import 'swiper/css';
import 'swiper/css/free-mode';
import 'swiper/css/navigation';
import 'swiper/css/thumbs';
import {Swiper, type SwiperProps, SwiperSlide} from 'swiper/react';
import {type Swiper as SwiperClass, type SwiperOptions} from 'swiper/types';
import {type UnitLessValues, reMediaQuery} from 'utils/v3/reMediaQuery';

const defaultSwiperBreakPoints: SwiperOptions['breakpoints'] = {
  2600: {
    spaceBetween: 90,
    slidesPerView: 1.73
  },
  1800: {
    spaceBetween: 88,
    slidesPerView: 1.7
  },
  1440: {
    spaceBetween: 70,
    slidesPerView: 1.7
  },
  1024: {
    spaceBetween: 30,
    slidesPerView: 1.73
  },
  768: {
    spaceBetween: 38,
    slidesPerView: 1.7
  },
  0: {
    slidesPerView: 1,
    spaceBetween: 45
  }
};

const Carousel = ({
  gradient = false,
  variant = 'dark',
  addRightOffset = false,
  children,
  swiperBreakPoints = defaultSwiperBreakPoints,
  slidesPerGroup = 1,
  selectedIndex = 0
}: CarouselComponentProps) => {
  const swiperRef = useRef<null | SwiperClass>(null);
  const [thumbsSwiper, setThumbsSwiper] = useState<SwiperClass>();
  const [isFirst, setIsFirst] = useState<boolean>(true);
  const [isLast, setIsLast] = useState<boolean>(false);
  const handleSliderChange = ({isEnd, isBeginning, snapIndex}: SwiperClass) => {
    console.log({swiperRef}, 'on slide change');
    // thumbsSwiper?.slideTo(snapIndex);
    setIsLast(false);
    setIsFirst(false);
    if (isEnd) {
      setIsLast(true);
      setIsFirst(false);
    }
    if (isBeginning) {
      setIsLast(false);
      setIsFirst(true);
    }
  };

  useEffect(() => {
    swiperRef.current?.slideTo(selectedIndex);
  }, [selectedIndex]);

  const onThumbsSliderInit = (swiper: SwiperClass) => setThumbsSwiper(swiper);

  console.log({swiperRef});

  const swiperProps: SwiperProps = {
    breakpoints: swiperBreakPoints,
    modules: [FreeMode, Navigation, Thumbs],
    slidesOffsetAfter: addRightOffset ? getSlidesOffsetAfter(swiperRef.current) : 0,
    slidesPerGroup,
    ...generalSwiperProps
  };

  const slideChildrenMapper = (child: ReactNode, i: number) => {
    return <SwiperSlide key={i}>{child}</SwiperSlide>;
  };

  const onNext = () => !isLast && swiperRef.current?.slideNext();
  const onPrev = () => !isFirst && swiperRef.current?.slidePrev();

  const swiperSlider = (
    <Swiper
      onRealIndexChange={handleSliderChange}
      thumbs={{swiper: thumbsSwiper && !thumbsSwiper.destroyed ? thumbsSwiper : null}}
      onSwiper={swiper => (swiperRef.current = swiper)}
      {...swiperProps}
    >
      {children && Children.map(children, slideChildrenMapper)}
    </Swiper>
  );

  return (
    <div>
      <LaptopContainer>
        <PrevButton disabled={isFirst}>
          <Arrow onClick={onPrev} direction="left" />
        </PrevButton>
        <CarouselWrapper>
          <MainWrapper gradient={gradient} variant={variant}>
            {swiperSlider}
          </MainWrapper>
        </CarouselWrapper>
        <NextButton disabled={isLast}>
          <Arrow onClick={onNext} direction="right" />
        </NextButton>
      </LaptopContainer>
      <TabletContainer>
        <CarouselWrapper>
          <MainWrapper gradient={gradient} variant={variant}>
            {swiperSlider}
          </MainWrapper>
        </CarouselWrapper>
        <ButtonContainer>
          <PrevButton disabled={isFirst}>
            <Arrow onClick={onPrev} direction="left" />
          </PrevButton>
          <NextButton disabled={isLast}>
            <Arrow onClick={onNext} direction="right" />
          </NextButton>
        </ButtonContainer>
      </TabletContainer>
    </div>
  );
};

export default Carousel;

const buttonGaps: UnitLessValues = [null, 0, 46, 60];

const PrevButton = styled.div<{disabled?: boolean}>`
  & svg path {
    fill: ${({disabled}) => disabled && 'rgba(255, 255, 255, 0.5)'};
  }
`;

const NextButton = styled.div<{disabled?: boolean}>`
  & svg path {
    fill: ${({disabled}) => disabled && 'rgba(255, 255, 255, 0.5)'};
  }
`;

const LaptopContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);

  @media (max-width: ${breakpoints.tablet}) {
    display: none;
  }

  ${PrevButton} {
    display: flex;
    align-items: center;
    justify-content: end;
    ${reMediaQuery({
      'padding-right': buttonGaps
    })}
  }

  ${NextButton} {
    display: flex;
    align-items: center;
    ${reMediaQuery({
      'padding-left': buttonGaps
    })}
  }
`;

const CarouselWrapper = styled.div`
  ${reMediaQuery({
    'max-width': ['100%', '100%', 1120, 1408]
  })}
`;

const TabletContainer = styled.div`
  @media (width > ${breakpoints.tablet}) {
    display: none;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  gap: 42px;
`;
