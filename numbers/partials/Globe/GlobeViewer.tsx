import {Billboard, Text, useCursor} from '@react-three/drei';
import {Can<PERSON>, use<PERSON>rame, useLoader, useThree} from '@react-three/fiber';
import {flushSync} from 'react-dom';

import {startTransition, useEffect, useLayoutEffect, useRef, useState} from 'react';
import styled from 'styled-components';
import * as THREE from 'three';
import {reMediaQuery} from 'utils/v3/reMediaQuery';
import './BumpColorMaterial';
import './FresnelGlowMaterial';

function latLngToVector3(lat: number, lng: number, radius = 2) {
  const phi = (90 - lat) * (Math.PI / 180);
  const theta = (lng + 180) * (Math.PI / 180);

  const x = -radius * Math.sin(phi) * Math.cos(theta);
  const y = radius * Math.cos(phi);
  const z = radius * Math.sin(phi) * Math.sin(theta);

  return new THREE.Vector3(x, y, z);
}

function LabelWithAutoBackground({
  text,
  position,
  fontSize,
  fontZPos,
  globeRef,
  onClick,
  visible,
  isSelected,
  geotagLeft,
  geotagRight,
  geotagCenter,
  isDisabled,
  renderOrder,
  zoom
}) {
  const ref = useRef<THREE.Mesh>(null);
  const {camera} = useThree();
  const textRef = useRef();
  const [bgSize, setBgSize] = useState([0, 0]);
  const [hovered, setHovered] = useState(false);
  const [labelColor, setLabelColor] = useState({bg: '#6939F9', font: 'white'});
  const globePosition = latLngToVector3(
    position.lat,
    position.lng,
    isSelected ? fontZPos + 0.01 : fontZPos
  );
  const [distanceFromCenter, setDistanceFromCenter] = useState(0);
  useCursor(hovered);

  useEffect(() => {
    setLabelColor({bg: 'white', font: 'rgba(31, 30, 39, 1)'});
    if (isSelected) {
      return;
    }

    if (hovered) {
      setLabelColor({bg: 'rgba(65, 57, 96, 1)', font: 'white'});
    } else {
      setLabelColor({bg: '#6939F9', font: 'white'});
    }
  }, [hovered, isSelected]);

  useEffect(() => {
    setHovered(false);
  }, [isDisabled]);

  useLayoutEffect(() => {
    setTimeout(() => {
      updateSizeBg();
    }, 2000);
  }, [text, fontSize]);

  const updateSizeBg = () => {
    if (textRef.current && textRef.current.geometry) {
      textRef.current.geometry.computeBoundingBox();
      const {boundingBox} = textRef.current.geometry;
      const width = boundingBox.max.x - boundingBox.min.x;
      const height = boundingBox.max.y - boundingBox.min.y;
      setBgSize([width, height]);
    }
  };

  useFrame(() => {
    if (ref.current && camera) {
      const labelWorldPosition = new THREE.Vector3();
      ref.current.getWorldPosition(labelWorldPosition);
      const globeCenter = new THREE.Vector3(0, 0, 0);
      const vecLabelToCamera = camera.position.clone().sub(labelWorldPosition).normalize();
      const vecGlobeCenterToLabel = labelWorldPosition.clone().sub(globeCenter).normalize();
      const dotProduct = vecLabelToCamera.dot(vecGlobeCenterToLabel);
      const threshold = 0.6;
      const fadeSharpness = 0.4;
      let newOpacity;

      if (dotProduct < threshold) {
        newOpacity = Math.max(0, (dotProduct - (threshold - fadeSharpness)) / fadeSharpness);
      } else {
        newOpacity = 1; // Fully visible
      }
      setDistanceFromCenter(newOpacity);

      ref.current.visible = visible && newOpacity > 0.01;
    }
  });

  function createRoundedRectShape(width, height, radius, roundedCorners = {}) {
    const shape = new THREE.Shape();

    const r = radius;
    const w = width;
    const h = height;

    // Default roundedCorners to all false if not passed
    const {tl = true, tr = true, br = false, bl = true} = roundedCorners;

    shape.moveTo(bl ? r : 0, 0);

    // Bottom edge
    shape.lineTo(w - (br ? r : 0), 0);
    if (br) shape.quadraticCurveTo(w, 0, w, r);

    // Right edge
    shape.lineTo(w, h - (tr ? r : 0));
    if (tr) shape.quadraticCurveTo(w, h, w - r, h);

    // Top edge
    shape.lineTo(tl ? r : 0, h);
    if (tl) shape.quadraticCurveTo(0, h, 0, h - r);

    // Left edge
    shape.lineTo(0, bl ? r : 0);
    if (bl) shape.quadraticCurveTo(0, 0, r, 0);

    // left triagle
    // shape.lineTo(0, 0);
    // shape.lineTo(h/2, 0);

    shape.lineTo(0, -h / 3);
    shape.lineTo(h / 3, 0);
    shape.lineTo(0, 0);

    return shape;
  }

  const xPadding = 0.3;
  const yPadding = 0.2;
  const shape = createRoundedRectShape(
    bgSize[0] + bgSize[1] * (xPadding * 2),
    bgSize[1] + bgSize[1] * (yPadding * 2),
    0.009 * zoom - 0.015,
    {
      tl: true,
      tr: true,
      br: true, // sharp corner
      bl: false
    }
  );

  return (
    <group ref={ref} position={globePosition} renderOrder={renderOrder}>
      <Billboard
        position={[-0.6 * bgSize[1], 0.6 * bgSize[1], 0]}
        visible={visible}
        renderOrder={renderOrder}
      >
        <mesh position={[bgSize[1] * -xPadding, bgSize[1] * -(yPadding + 0.05), -0.001]}>
          <shapeGeometry args={[shape]} />
          <meshBasicMaterial
            color={labelColor.bg}
            transparent
            opacity={distanceFromCenter}
            transparent={true}
            depthTest={false}
            depthWrite={false}
          />
        </mesh>

        {/* 3D Text */}
        <Text
          ref={textRef}
          fontSize={fontSize}
          color={labelColor.font}
          anchorX="left"
          anchorY="bottom"
          onPointerOver={e => {
            if (!isDisabled) {
              e.stopPropagation();
              setHovered(true);
            }
          }}
          onPointerOut={e => {
            if (!isDisabled) {
              e.stopPropagation();
              setHovered(false);
            }
          }}
          onClick={e => {
            if (visible) {
              e.stopPropagation();
              onClick(position);
            }
          }}
        >
          {text}
          <meshStandardMaterial
            attach="material"
            opacity={distanceFromCenter}
            transparent={true}
            depthTest={false}
            depthWrite={false}
          />
        </Text>
      </Billboard>
    </group>
  );
}

function RotatingGlobe(props) {
  const globeRef = useRef();
  const glowRef = useRef();
  const [rotationUpdate, setRotationUpdate] = useState({
    x: props.rotate.x,
    y: props.rotate.y,
    z: props.rotate.z,
    zoom: props.rotate.zoom
  });

  const [globeLoc, setGlobeLoc] = useState({x: 0, y: 0, z: 2.07});
  const earthTexture = useLoader(THREE.TextureLoader, props.earthTexture);
  const bumpTexture = useLoader(THREE.TextureLoader, props.bumpMap);
  const alphaTexture = useLoader(THREE.TextureLoader, props.alphaMap01);
  const alphafullTexture = useLoader(THREE.TextureLoader, props.alphaMap);

  //
  const [isDragging, setDragging] = useState(false);
  const start = useRef([0, 0]);
  useEffect(() => {
    const preventZoom = e => {
      // Block pinch-to-zoom if it's a ctrl/cmd + scroll gesture
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
      }
    };

    window.addEventListener('wheel', preventZoom, {passive: false});

    return () => {
      window.removeEventListener('wheel', preventZoom);
    };
  }, []);

  const handlePointerDown = e => {
    setDragging(true);
    start.current = [e.clientX, e.clientY];
    window.addEventListener('pointerup', handlePointerUp);
  };

  const handlePointerUp = () => {
    setDragging(false);
    window.removeEventListener('pointerup', handlePointerUp);
  };

  const handlePointerMove = e => {
    if (!isDragging || !globeRef.current) return;
    const [startX, startY] = start.current;
    const deltaX = e.clientX - startX;
    const deltaY = e.clientY - startY;

    // set current
    start.current = [e.clientX, e.clientY];

    flushSync(() => {
      setRotationUpdate(prev => ({
        ...prev,
        y: prev.y + deltaX * 0.005, // adjust sensitivity
        x: prev.x + deltaY * 0.005
      }));
    });
  };

  const handleWheel = e => {
    if (props.onWheel) {
      props.onWheel(e.deltaY, globeRef);
    }
  };

  useEffect(() => {
    startTransition(() => {
      setRotationUpdate({
        x: props.rotate.x,
        y: props.rotate.y,
        z: props.rotate.z,
        zoom: props.rotate.zoom
      });
    });
  }, [props]);

  useFrame(state => {
    if (globeRef.current) {
      globeRef.current.rotation.y = THREE.MathUtils.lerp(
        globeRef.current.rotation.y,
        rotationUpdate.y,
        0.05
      );
      globeRef.current.rotation.x = THREE.MathUtils.lerp(
        globeRef.current.rotation.x,
        rotationUpdate.x,
        0.05
      );
    }
  });

  return (
    <group
      ref={globeRef}
      onPointerDown={handlePointerDown}
      onPointerUp={handlePointerUp}
      onPointerMove={handlePointerMove}
      // onPointerOut={handlePointerUp}
      onWheel={handleWheel}
      scale={[1, 1, 1]}
    >
      <mesh>
        <sphereGeometry args={[2, 75, 75]} />
        <meshStandardMaterial
          map={earthTexture}
          // color="#ff0000"
          bumpMap={bumpTexture}
          bumpScale={0.01}
          alphaMap={alphaTexture}
          lightMap={alphafullTexture}
          lightMapIntensity={0.7}
          transparent
          side={THREE.DoubleSide}
          // aoMap={alphaTexture}
          // envMap={alphaTexture}
          // envMapIntensity={10}
          // normalMap={earthTexture}
          // normalScale={[1, 10]}
          metalnessMap={alphaTexture}
          roughness={0.6}
          metalness={0.75} // must be > 0 for map to take effect
          // envMapIntensity={5.0}
        />
      </mesh>
      <mesh>
        <sphereGeometry args={[2, 75, 75]} />
        <fresnelGlowMaterial
          ref={glowRef}
          color="white"
          blending={THREE.AdditiveBlending}
          transparent
        />
      </mesh>

      {props.locations &&
        props.locations.map((loc, index) => {
          const isSelected = loc.id === props?.selectedLocation?.id;
          if (isSelected) {
            loc.priority = 1;
          }
          return (
            <LabelWithAutoBackground
              key={index}
              text={loc.countryName}
              position={loc}
              fontSize={computeFontSize(props.zoom)}
              fontZPos={computeLabelZ(props.zoom) + index * 0.0}
              globeRef={globeRef}
              onClick={props.onClick}
              isSelected={isSelected}
              visible
              geotagLeft={props.geotagLeft}
              geotagRight={props.geotagRight}
              geotagCenter={props.geotagCenter}
              isDisabled={isDragging}
              renderOrder={index + 1}
              zoom={props.zoom}
            />
          );
        })}

      {/* dont delete this, will use for bug debugging */}
      {/* <mesh position={[globeLoc.x, globeLoc.y, globeLoc.z]}>
        <sphereBufferGeometry args={[0.1, 0.1, 0]} />
        <meshBasicMaterial color="yellow" />
      </mesh> */}
    </group>
  );
}

function ZoomController({targetZ = 3}) {
  const {camera} = useThree();
  const [zoomTarget, setZoomTarget] = useState(targetZ);

  useEffect(() => {
    setZoomTarget(targetZ); // Trigger when prop changes
  }, [targetZ]);

  useFrame(() => {
    camera.position.z = THREE.MathUtils.lerp(camera.position.z, zoomTarget, 0.05); // easing
    camera.updateProjectionMatrix();
  });

  return null;
}

function computeFontSize(zoom) {
  return 0.030714 * zoom - 0.05714;
}

function computeLabelZ(zoom) {
  return 0.05 * zoom + 1.9;
}

export default function GlobeViewer({
  branch,
  locations,
  onClick,
  earthTexture,
  bumpMap,
  alphaMap,
  alphaMap01,
  geotagLeft,
  geotagRight,
  geotagCenter,
  handIcon
}) {
  const [rotationUpdate, setRotationUpdate] = useState({x: 0, y: 0, z: 0, zoom: 4});
  const [isShowGuide, setIsShowGuide] = useState(false);

  const canvasRef = useRef();

  useLayoutEffect(() => {
    if (canvasRef.current) {
      setTimeout(() => {
        setIsShowGuide(true);
      }, 3000);
    }
  }, []);

  useEffect(() => {
    if (branch) {
      const latRad = THREE.MathUtils.degToRad(branch.lat);
      const lngRad = THREE.MathUtils.degToRad(branch.lng);
      const branchRotationX = latRad;
      const branchRotationY = -lngRad - Math.PI / 2;
      startTransition(() => {
        setRotationUpdate({
          x: branchRotationX,
          y: branchRotationY,
          z: 0,
          zoom: rotationUpdate.zoom
        });
      });
    }
  }, [branch]);

  const onGuideClick = () => {
    setIsShowGuide(false);
  };

  return (
    <Container>
      {isShowGuide && (
        <Guide onClick={onGuideClick}>
          <GuideIcon>
            <img src={handIcon} alt="globe" />
          </GuideIcon>
          Drag to rotate <br />
          the map
        </Guide>
      )}
      <Canvas ref={canvasRef} camera={{position: [0, 0, 6], fov: 60}}>
        <pointLight color={'#ffffff'} position={[0, 0, 10]} intensity={0.75} />
        <ambientLight color={'grey'} intensity={1.1} />
        <RotatingGlobe
          rotate={{x: rotationUpdate.x, y: rotationUpdate.y, z: rotationUpdate.z}}
          locations={locations}
          zoom={rotationUpdate.zoom}
          onClick={onClick}
          selectedLocation={branch}
          earthTexture={earthTexture}
          bumpMap={bumpMap}
          alphaMap={alphaMap}
          alphaMap01={alphaMap01}
          geotagLeft={geotagLeft}
          geotagRight={geotagRight}
          geotagCenter={geotagCenter}
        />

        <ZoomController targetZ={rotationUpdate.zoom} />
      </Canvas>
    </Container>
  );
}

const Container = styled.div`
  position: relative;
  height: 100%;
`;
// background-image: url("paper.gif");

const Guide = styled.div`
  position: absolute;
  display: flex;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  background: rgba(65, 57, 96, 0.2);
  backdrop-filter: blur(6px);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-family: 'Sharp Grotesk Medium';
  font-style: normal;
  font-weight: 500;
  ${reMediaQuery({
    gap: [15, 15, 20, 20],
    padding: [20, 20, 24, 32],
    'font-size': [16, 16, 18, 22],
    'line-height': ['150%', '150%', '150%', '150%'],
    'border-radius': [8, 8, 8, 8],
    width: [215, 215, 250, 310]
  })}
`;

const GuideIcon = styled.div`
  ${reMediaQuery({
    width: [40, 40, 50, 60],
    height: [40, 40, 50, 60]
  })}
`;
